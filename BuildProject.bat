@echo off
echo Killing any existing UnrealBuildTool processes...
taskkill /F /IM "UnrealBuildTool.exe" 2>nul

echo Regenerating project files...
"F:\Unreal\UE_5.5\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe" -projectfiles -project="F:\teamgravitydev\StormEscape\StormEscape.uproject" -game -rocket -progress

echo Building project...
"F:\Unreal\UE_5.5\Engine\Build\BatchFiles\Build.bat" StormEscapeEditor Win64 Development -Project="F:\teamgravitydev\StormEscape\StormEscape.uproject" -WaitMutex -FromMsBuild

pause
