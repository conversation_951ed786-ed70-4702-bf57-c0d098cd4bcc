// Fill out your copyright notice in the Description page of Project Settings.


#include "StormEscapePlayerController.h"

#include "BaseFirstPersonCharacter.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "BlueprintLibraries/UIHelperLibrary.h"
#include "Components/GameplayVoiceChatComponent.h"
#include "Core/Lobby/LobbyGameMode.h"
#include "Core/StormEscapeGameInstance.h"
#include "GameFramework/Character.h"
#include "GameFramework/PawnMovementComponent.h"
#include "Interfaces/DamageInterface.h"
#include "Settings/CustomUISettings.h"
#include "UI/BaseMainLayerUI.h"
#include "UI/UIManager.h"

AStormEscapePlayerController::AStormEscapePlayerController()
{
	VoiceChatComp = CreateDefaultSubobject<UGameplayVoiceChatComponent>(TEXT("Gameplay VoiceChat"));
	AddOwnedComponent(VoiceChatComp);

	// Initialize input context tracking variables
	CurrentPauseMenuWidget = nullptr;
	PreviousInputMappingContext = nullptr;
}

void AStormEscapePlayerController::ShowErrorPopup_Implementation(const FString& ErrorHead, const FString& ErrorBody)
{
	IControllerInterface::ShowErrorPopup_Implementation(ErrorHead, ErrorBody);
	
	if (IsLocalController())
	{
		// Show the error popup
		UUIHelperLibrary::ShowErrorPopup(ErrorHead, ErrorBody, this);
	}else
	{
		// Call the function on the client
		ClientShowErrorPopup(ErrorHead, ErrorBody);
	}
}

void AStormEscapePlayerController::KickToMainMenu_Implementation(const FString& Reason)
{
	IControllerInterface::KickToMainMenu_Implementation(Reason);

	if (IsLocalController())
	{
		if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
		{
			GI->KickAndReturnToMenu(Reason);
		}
		
	}else
	{
		// Call the function on the client
		Client_KickToMainMenu(Reason);
	}
	
}

void AStormEscapePlayerController::ClientShowLoadingScreen_Implementation()
{
	//get the game instance and toggle the loading screen
	if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
	{
		GI->BeginLoadingScreen("Loading...");
	}
}

void AStormEscapePlayerController::ClientShowErrorPopup_Implementation(const FString& ErrorHead,
                                                                       const FString& ErrorBody)
{
	UUIHelperLibrary::ShowErrorPopup(ErrorHead, ErrorBody, this);
}

void AStormEscapePlayerController::BeginPlay()
{
	Super::BeginPlay();

	GameHUD = Cast<AStormEscapeHUD>(GetHUD());

	// get the game mode and cast it to lobby game mode and bind to on match started
	if (ALobbyGameMode* GameMode = Cast<ALobbyGameMode>(GetWorld()->GetAuthGameMode()))
	{
		GameMode->OnMatchStarted.AddDynamic(this, &AStormEscapePlayerController::ClientShowLoadingScreen);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Game mode is not of type ALobbyGameMode"));
	}

	if (IsLocalController())
	{
		//Initialize the UI manager
		if (MainLayerUIClass)
		{
			MainLayerUI = CreateWidget<UBaseMainLayerUI>(this, MainLayerUIClass);
			MainLayerUI->AddToViewport();
			UUIManager::Initialize(this, MainLayerUI);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Main layer UI class is not set"));
		}

		//Get the Pause Menu class and Cache it
		if (const UCustomUISettings* UISettings = GetDefault<UCustomUISettings>())
		{
			PauseMenuClass = UISettings->PauseMenuWidgetClass;
		}
	}
	
}

void AStormEscapePlayerController::SetupInputComponent()
{
	Super::SetupInputComponent();

	if (UEnhancedInputComponent* EnhancedInput = Cast<UEnhancedInputComponent>(InputComponent))
	{
		if (PauseAction)
		{
			EnhancedInput->BindAction(PauseAction, ETriggerEvent::Started, this, &AStormEscapePlayerController::TogglePauseMenu);
		}
	}
}

void AStormEscapePlayerController::Client_KickToMainMenu_Implementation(const FString& String)
{
	if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
	{
		GI->KickAndReturnToMenu(String);
	}
}

void AStormEscapePlayerController::TogglePauseMenu()
{
	// Check if pause menu is already open
	if (CurrentPauseMenuWidget && IsValid(CurrentPauseMenuWidget))
	{
		// Close the pause menu
		CurrentPauseMenuWidget->DeactivateWidget();
		return;
	}

	// Open the pause menu
	CurrentPauseMenuWidget = UUIManager::AddWidgetToStack(this, PauseMenuClass, EWidgetLayer::Menu, EInputModeType::UIOnly, true);

	if (CurrentPauseMenuWidget)
	{
		// Switch to UI input context when pause menu opens
		SwitchToUIInputContext();

		// Bind to the widget's destruction to restore input context
		CurrentPauseMenuWidget->OnDeactivated().AddUObject(this, &AStormEscapePlayerController::OnPauseMenuClosed);
	}
}

void AStormEscapePlayerController::NotifyTornadoHit(AActor* tornado)
{
	if (GetCharacter()->GetClass()->ImplementsInterface(UDamageInterface::StaticClass()))
	{
		IDamageInterface::Execute_Kill(GetCharacter(), tornado);
	}
}

void AStormEscapePlayerController::SwitchToUIInputContext()
{
	if (!IsLocalController() || !UIControlsInputMapping)
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot switch to UI input context: LocalController=%s, UIControlsInputMapping=%s"),
			IsLocalController() ? TEXT("true") : TEXT("false"),
			UIControlsInputMapping ? TEXT("valid") : TEXT("null"));
		return;
	}

	UEnhancedInputLocalPlayerSubsystem* InputSubsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(GetLocalPlayer());
	if (!InputSubsystem)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to get Enhanced Input subsystem"));
		return;
	}

	// Store the current active mapping context from the character
	if (ABaseFirstPersonCharacter* FirstPersonChar = Cast<ABaseFirstPersonCharacter>(GetCharacter()))
	{
		PreviousInputMappingContext = FirstPersonChar->GetInputMappingContext();
		UE_LOG(LogTemp, Log, TEXT("Stored previous input mapping context from character"));
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Could not get character to store previous input mapping context"));
	}

	// Remove the current game input mapping context if it exists
	if (PreviousInputMappingContext)
	{
		InputSubsystem->RemoveMappingContext(PreviousInputMappingContext);
	}

	// Add the UI controls mapping context with higher priority
	InputSubsystem->AddMappingContext(UIControlsInputMapping, 1);

	UE_LOG(LogTemp, Log, TEXT("Switched to UI input mapping context"));
}

void AStormEscapePlayerController::RestorePreviousInputContext()
{
	if (!IsLocalController())
	{
		return;
	}

	UEnhancedInputLocalPlayerSubsystem* InputSubsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(GetLocalPlayer());
	if (!InputSubsystem)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to get Enhanced Input subsystem"));
		return;
	}

	// Remove the UI controls mapping context
	if (UIControlsInputMapping)
	{
		InputSubsystem->RemoveMappingContext(UIControlsInputMapping);
		UE_LOG(LogTemp, Log, TEXT("Removed UI controls mapping context"));
	}

	// Restore the previous mapping context if we have one
	if (PreviousInputMappingContext)
	{
		InputSubsystem->AddMappingContext(PreviousInputMappingContext, 0);
		UE_LOG(LogTemp, Log, TEXT("Restored previous input mapping context"));
	}
	else
	{
		// Fallback: Try to restore the character's input mapping directly
		if (ABaseFirstPersonCharacter* FirstPersonChar = Cast<ABaseFirstPersonCharacter>(GetCharacter()))
		{
			UInputMappingContext* CharacterInputMapping = FirstPersonChar->GetInputMappingContext();
			if (CharacterInputMapping)
			{
				InputSubsystem->AddMappingContext(CharacterInputMapping, 0);
				UE_LOG(LogTemp, Log, TEXT("Restored character input mapping as fallback"));
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Character has no input mapping to restore"));
			}
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("No character found to restore input mapping"));
		}
	}

	// Clear the stored context
	PreviousInputMappingContext = nullptr;
}

void AStormEscapePlayerController::OnPauseMenuClosed()
{
	// Restore the previous input mapping context
	RestorePreviousInputContext();

	// Clear the reference to the pause menu widget
	CurrentPauseMenuWidget = nullptr;

	UE_LOG(LogTemp, Log, TEXT("Pause menu closed, input context restored"));
}
