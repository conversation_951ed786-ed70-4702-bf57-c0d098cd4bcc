// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "InputActionValue.h"
#include "Components/CameraShakeComponent.h"
#include "Components/CharacterAttributesComponent.h"
#include "Components/InteractComponent.h"
#include "Interfaces/VoiceEmitterInterface.h"
#include "Interfaces/DamageInterface.h"
#include "BaseFirstPersonCharacter.generated.h"

class UOdinSynthComponent;
class UInputMappingContext;
class UInputAction;
class UCameraComponent;
class USpringArmComponent;
class ULegacyCameraShake;
class UPhysicsConstraintComponent;
class UInteractComponent;

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnCharacterDeath);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnCharacterRevive);

UCLASS()
class STORMESCAPE_API ABaseFirstPersonCharacter : public ACharacter, public IVoiceEmitterInterface, public IDamageInterface
{
	GENERATED_BODY()

public:
	ABaseFirstPersonCharacter();

	virtual void Tick(float DeltaTime) override;

	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	void DealDamage_Implementation(float damageAmount, bool bIsDamageOverTime, AActor* DamageCauser) override;

	EActorHealthStatus GetHealthStatus_Implementation() const;

	void Kill_Implementation(AActor* DamageCauser) override;

	void Incapacitate_Implementation(AActor* sourceActor, float duration, float pushStrength) override;

	void Resurrect_Implementation(AActor* Healer, float healingAmount) override;

	void Landed(const FHitResult& Hit) override;

	UPROPERTY(ReplicatedUsing = OnRep_PlayerId)
	FGuid PlayerId;

	UPROPERTY(BlueprintReadWrite)
	TArray<UOdinSynthComponent*> OdinSynths;

	UFUNCTION()
	void OnRep_PlayerId();

	UFUNCTION(BlueprintCallable)
	void InitVoiceChat();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	bool IsPlayerDead();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	bool IsPlayerActive();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	bool CanResurrect();

	UOdinSynthComponent* CreateVoiceSynthComponent_Implementation(const FVoiceRoomData& roomData, int64 peerID, UOdinPlaybackMedia* media) override;

	UPROPERTY(BlueprintAssignable)
	FOnCharacterDeath OnDeathDelegate;

	UPROPERTY(BlueprintAssignable)
	FOnCharacterDeath OnReviveDelegate;

protected:
	virtual void BeginPlay() override;

	UFUNCTION()
	void WakePlayerUp();

	void UpdateMovementSpeed();

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UCameraShakeComponent* cameraShakeComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UCharacterAttributesComponent* attributes;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UInteractComponent* interactComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UCameraComponent* camera;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	USpringArmComponent* springArm;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* heldItemSlot;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UPhysicsConstraintComponent* grabConstraint;

	UPROPERTY(EditDefaultsOnly, Category = "Properties")
	TArray<UMaterialInterface*> owningPlayerMaterials;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputMappingContext* inputMapping;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* moveAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* lookAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* sprintAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* crouchAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* jumpAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* interactAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* dropItemAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* itemAction;

	UPROPERTY(Replicated, BlueprintReadOnly)
	bool bIsRunning = false;

	UPROPERTY(Replicated, BlueprintReadOnly)
	bool bIsCrouching = false;

	UPROPERTY(Replicated, BlueprintReadOnly)
	bool bIsInteracting;

	UPROPERTY(Replicated, BlueprintReadOnly)
	EActorHealthStatus healthStatus = EActorHealthStatus::Awake;

	UFUNCTION(BlueprintCallable)
	void StartCameraFade(const bool fadeIn, const float duration = 3.f);

	UFUNCTION(BlueprintCallable)
	void StopCameraFade();

	UFUNCTION(BlueprintCallable)
	void PerformDeath();

	UFUNCTION(BlueprintCallable)
	void StartRagdoll(FVector_NetQuantize impulse);

	UFUNCTION(BlueprintCallable)
	void StopRagdoll();

	UPROPERTY(BlueprintReadOnly)
	bool bIsRagdolling;

	UFUNCTION(BlueprintCallable)
	void ToggleUI(bool bShowUI);

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnSpeedMultiplierChanged")
	void K2_OnSpeedMultiplierChanged(float multiplier);

	/** Get the input mapping context for this character */
	UFUNCTION(BlueprintCallable, Category = "Input")
	UInputMappingContext* GetInputMappingContext() const { return inputMapping; }

private:
	APlayerController* playerController;
	FTimerHandle drainStaminaHandle;
	FTimerHandle recoverStaminaHandle;
	FTimerHandle shakeTimer;
	FTransform springArmOffset;
	FTransform meshOffset;

	bool bCanStartWalkingShake;
	bool bCanStartRunningShake;

	void InitializePlayer();

	void SetInputMapping();

	UFUNCTION()
	void EnablePlayerInput();

	void ApplyOwnerMaterials();

	void PerformIdleShake();

	UFUNCTION()
	void OnSpeedChangedByInteraction(float speedModifier);

	float GetSpeedMultiplier() const;

	void Move(const FInputActionValue& inputValue);

	void Sprint();

	UFUNCTION(Server, Reliable)
	void ServerSprint();

	UFUNCTION(Client, Reliable)
	void ClientSprint();

	void StopSprinting();

	UFUNCTION(Server, Reliable)
	void ServerStopSprinting();

	UFUNCTION(Client, Reliable)
	void ClientStopSprinting();

	void PerformJump();

	void PerformCrouch();

	void StopCrouching();

	void Look(const FInputActionValue& inputValue);

	void DrainStamina();

	void RecoverStamina();

	void Interact();

	void StopInteracting();

	void DropItem();

	void TriggerItemAction();

	void StopItemAction();

	UFUNCTION(Server, Reliable)
	void Server_TriggerItemAction();

	UFUNCTION(Server, Reliable)
	void Server_StopItemAction();

	UFUNCTION(Server, Reliable)
	void Server_Death();

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_Death();

	UFUNCTION()
	void DetachCameraFromBody();

	FTimerHandle deathCameraTimer;

	UFUNCTION(Server, Reliable)
	void Server_UpdateCapsule();

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_UpdateCapsule(FVector_NetQuantize ragdollLocation);

	UFUNCTION(Server, Reliable)
	void Server_Incapacitate(AActor* sourceActor, float duration = 0.f, float pushStrength = 0.f);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_Incapacitate(AActor* sourceActor, float duration = 0.f, float pushStrength = 0.f);

	UFUNCTION(Server, Reliable)
	void Server_WakeUp();

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_WakeUp();

	FTimerHandle stunTimer;

	UFUNCTION(Server, Reliable)
	void Server_Resurrect(float healingAmount);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_Resurrect(FTransform cameraTransform, float healingAmount);

	void PushPlayerOffCollision(TArray<AActor*> ovelappingActors, float DeltaTime);
};
